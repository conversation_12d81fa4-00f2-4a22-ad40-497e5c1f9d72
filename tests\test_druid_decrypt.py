#!/usr/bin/env python3
"""
Druid密码解密功能测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.druid_decrypt import decrypt_druid_password, encrypt_druid_password


def test_encrypt_decrypt():
    """测试加密和解密功能"""
    print("=== Druid密码加密/解密测试 ===")
    
    # 测试密码
    test_passwords = [
        "123456",
        "password",
        "mysql_password",
        "复杂密码!@#$%^&*()",
        "test123"
    ]
    
    for password in test_passwords:
        print(f"\n测试密码: {password}")
        
        try:
            # 加密
            encrypted = encrypt_druid_password(password)
            print(f"加密结果: {encrypted}")
            
            # 解密
            decrypted = decrypt_druid_password(encrypted)
            print(f"解密结果: {decrypted}")
            
            # 验证
            if password == decrypted:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")


def test_config_integration():
    """测试配置模块集成"""
    print("\n=== 配置模块集成测试 ===")
    
    try:
        from config.app_config import decrypt_druid_password as config_decrypt
        
        # 测试密码
        test_password = "test123"
        
        # 先加密
        encrypted = encrypt_druid_password(test_password)
        print(f"加密的测试密码: {encrypted}")
        
        # 使用配置模块的解密函数
        decrypted = config_decrypt(encrypted)
        print(f"配置模块解密结果: {decrypted}")
        
        if test_password == decrypted:
            print("✅ 配置模块集成测试通过")
        else:
            print("❌ 配置模块集成测试失败")
            
    except Exception as e:
        print(f"❌ 配置模块集成测试失败: {e}")


def test_mysql_config():
    """测试MySQL配置获取"""
    print("\n=== MySQL配置测试 ===")
    
    try:
        # 设置测试环境变量
        os.environ['MYSQL_HOST'] = 'localhost'
        os.environ['MYSQL_USER'] = 'test_user'
        os.environ['MYSQL_DB'] = 'test_db'
        
        # 测试明文密码
        os.environ['MYSQL_PASSWORD'] = 'plain_password'
        os.environ['MYSQL_PASSWORD_ENCRYPTED'] = 'false'
        
        from config.app_config import get_mysql_config
        
        config = get_mysql_config()
        print(f"明文密码配置: {config['password']}")
        
        # 测试加密密码
        encrypted_password = encrypt_druid_password('encrypted_password')
        os.environ['MYSQL_PASSWORD'] = encrypted_password
        os.environ['MYSQL_PASSWORD_ENCRYPTED'] = 'true'
        
        config = get_mysql_config()
        print(f"解密后的密码配置: {config['password']}")
        
        if config['password'] == 'encrypted_password':
            print("✅ MySQL配置解密测试通过")
        else:
            print("❌ MySQL配置解密测试失败")
            
    except Exception as e:
        print(f"❌ MySQL配置测试失败: {e}")
    finally:
        # 清理环境变量
        for key in ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_DB', 'MYSQL_PASSWORD', 'MYSQL_PASSWORD_ENCRYPTED']:
            if key in os.environ:
                del os.environ[key]


if __name__ == '__main__':
    test_encrypt_decrypt()
    test_config_integration()
    test_mysql_config()
    print("\n=== 测试完成 ===")
