#!/usr/bin/env python3
"""
测试不同的RSA加密解密方法
验证Druid的实际实现方式
"""

import base64
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend

# Druid默认的RSA密钥对
DEFAULT_PRIVATE_KEY_STRING = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAocbCrurZGbC5GArEHKlAfDSZi7gFBnd4yxOt0rwTqKBFzGyhtQLu5PRKjEiOXVa95aeIIBJ6OhC2f8FjqFUpawIDAQABAkAPejKaBYHrwUqUEEOe8lpnB6lBAsQIUFnQI/vXU4MV+MhIzW0BLVZCiarIQqUXeOhThVWXKFt8GxCykrrUsQ6BAiEA4vMVxEHBovz1di3aozzFvSMdsjTcYRRo82hS5Ru2/OECIQC2fAPoXixVTVY7bNMeuxCP4954ZkXp7fEPDINCjcQDywIgcc8XLkkPcs3Jxk7uYofaXaPbg39wuJpEmzPIxi3k0OECIGubmdpOnin3HuCP/bbjbJLNNoUdGiEmFL5hDI4UdwAdAiEAtcAwbm08bKN7pwwvyqaCBC//VnEWaq39DCzxr+Z2EIk="
DEFAULT_PUBLIC_KEY_STRING = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ=="


def test_standard_rsa():
    """测试标准的RSA加密解密（公钥加密，私钥解密）"""
    print("=== 测试标准RSA加密解密 ===")
    
    try:
        # 加载密钥
        private_key_bytes = base64.b64decode(DEFAULT_PRIVATE_KEY_STRING)
        public_key_bytes = base64.b64decode(DEFAULT_PUBLIC_KEY_STRING)
        
        private_key = serialization.load_der_private_key(
            private_key_bytes, password=None, backend=default_backend()
        )
        public_key = serialization.load_der_public_key(
            public_key_bytes, backend=default_backend()
        )
        
        # 测试数据
        test_data = "test123"
        print(f"原始数据: {test_data}")
        
        # 公钥加密
        encrypted = public_key.encrypt(
            test_data.encode('utf-8'),
            padding.PKCS1v15()
        )
        encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')
        print(f"公钥加密结果: {encrypted_b64}")
        
        # 私钥解密
        decrypted = private_key.decrypt(encrypted, padding.PKCS1v15())
        decrypted_text = decrypted.decode('utf-8')
        print(f"私钥解密结果: {decrypted_text}")
        
        if test_data == decrypted_text:
            print("✅ 标准RSA测试成功")
        else:
            print("❌ 标准RSA测试失败")
            
    except Exception as e:
        print(f"❌ 标准RSA测试失败: {e}")


def test_reverse_rsa():
    """测试反向RSA（私钥加密，公钥解密）"""
    print("\n=== 测试反向RSA（私钥加密，公钥解密）===")
    
    try:
        # 加载密钥
        private_key_bytes = base64.b64decode(DEFAULT_PRIVATE_KEY_STRING)
        public_key_bytes = base64.b64decode(DEFAULT_PUBLIC_KEY_STRING)
        
        private_key = serialization.load_der_private_key(
            private_key_bytes, password=None, backend=default_backend()
        )
        public_key = serialization.load_der_public_key(
            public_key_bytes, backend=default_backend()
        )
        
        # 测试数据
        test_data = "test123"
        print(f"原始数据: {test_data}")
        
        # 私钥"加密"（实际上是签名）
        signature = private_key.sign(
            test_data.encode('utf-8'),
            padding.PKCS1v15(),
            None  # 不使用哈希算法，直接签名
        )
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        print(f"私钥签名结果: {signature_b64}")
        
        # 公钥"解密"（实际上是验证签名）
        # 注意：标准的RSA签名验证不会返回原始数据
        # 我们需要使用低级别的RSA操作
        
        print("注意：标准RSA签名验证不能直接恢复原始数据")
        
    except Exception as e:
        print(f"❌ 反向RSA测试失败: {e}")


def test_low_level_rsa():
    """测试低级别的RSA操作"""
    print("\n=== 测试低级别RSA操作 ===")
    
    try:
        # 加载密钥
        private_key_bytes = base64.b64decode(DEFAULT_PRIVATE_KEY_STRING)
        public_key_bytes = base64.b64decode(DEFAULT_PUBLIC_KEY_STRING)
        
        private_key = serialization.load_der_private_key(
            private_key_bytes, password=None, backend=default_backend()
        )
        public_key = serialization.load_der_public_key(
            public_key_bytes, backend=default_backend()
        )
        
        # 获取RSA参数
        private_numbers = private_key.private_numbers()
        public_numbers = private_numbers.public_numbers
        n = public_numbers.n
        e = public_numbers.e
        d = private_numbers.d
        
        print(f"RSA参数:")
        print(f"n (模数): {n}")
        print(f"e (公钥指数): {e}")
        print(f"d (私钥指数): {d}")
        
        # 测试数据
        test_data = "test123"
        data_bytes = test_data.encode('utf-8')
        
        # 手动添加PKCS1填充
        key_size = (n.bit_length() + 7) // 8
        max_data_length = key_size - 11
        
        if len(data_bytes) > max_data_length:
            raise Exception(f"数据太长，最大支持 {max_data_length} 字节")
        
        # 构造PKCS1填充
        padding_length = key_size - len(data_bytes) - 3
        padded_data = bytes([0x00, 0x01]) + bytes([0xFF] * padding_length) + bytes([0x00]) + data_bytes
        
        print(f"填充后的数据长度: {len(padded_data)} 字节")
        
        # 转换为整数
        padded_int = int.from_bytes(padded_data, byteorder='big')
        
        # 方法1：私钥"加密"
        encrypted_int = pow(padded_int, d, n)
        encrypted_bytes = encrypted_int.to_bytes(key_size, byteorder='big')
        encrypted_b64 = base64.b64encode(encrypted_bytes).decode('utf-8')
        print(f"私钥加密结果: {encrypted_b64}")
        
        # 方法1：公钥"解密"
        decrypted_int = pow(encrypted_int, e, n)
        decrypted_bytes = decrypted_int.to_bytes(key_size, byteorder='big')
        
        # 移除填充
        if len(decrypted_bytes) >= 2 and decrypted_bytes[0] == 0x00 and decrypted_bytes[1] == 0x01:
            # 找到填充结束位置
            separator_index = -1
            for i in range(2, len(decrypted_bytes)):
                if decrypted_bytes[i] == 0x00:
                    separator_index = i
                    break
            
            if separator_index != -1:
                actual_data = decrypted_bytes[separator_index + 1:]
                decrypted_text = actual_data.decode('utf-8')
                print(f"公钥解密结果: {decrypted_text}")
                
                if test_data == decrypted_text:
                    print("✅ 低级别RSA操作成功（私钥加密，公钥解密）")
                else:
                    print("❌ 低级别RSA操作失败")
            else:
                print("❌ 找不到填充分隔符")
        else:
            print("❌ 无效的PKCS1填充")
            
    except Exception as e:
        print(f"❌ 低级别RSA测试失败: {e}")


if __name__ == '__main__':
    test_standard_rsa()
    test_reverse_rsa()
    test_low_level_rsa()
