#!/usr/bin/env python3
"""
Druid密码解密工具
用于解密使用Druid默认RSA密钥加密的MySQL密码
"""

import base64
import sys
import argparse
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend


# Druid默认的RSA密钥对
DEFAULT_PRIVATE_KEY_STRING = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAocbCrurZGbC5GArEHKlAfDSZi7gFBnd4yxOt0rwTqKBFzGyhtQLu5PRKjEiOXVa95aeIIBJ6OhC2f8FjqFUpawIDAQABAkAPejKaBYHrwUqUEEOe8lpnB6lBAsQIUFnQI/vXU4MV+MhIzW0BLVZCiarIQqUXeOhThVWXKFt8GxCykrrUsQ6BAiEA4vMVxEHBovz1di3aozzFvSMdsjTcYRRo82hS5Ru2/OECIQC2fAPoXixVTVY7bNMeuxCP4954ZkXp7fEPDINCjcQDywIgcc8XLkkPcs3Jxk7uYofaXaPbg39wuJpEmzPIxi3k0OECIGubmdpOnin3HuCP/bbjbJLNNoUdGiEmFL5hDI4UdwAdAiEAtcAwbm08bKN7pwwvyqaCBC//VnEWaq39DCzxr+Z2EIk="
DEFAULT_PUBLIC_KEY_STRING = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ=="


def decrypt_druid_password(encrypted_password, public_key_string=None):
    """
    解密Druid加密的密码
    注意：Druid使用私钥加密，公钥解密的方式

    Args:
        encrypted_password (str): 加密后的密码（Base64编码）
        public_key_string (str, optional): 公钥字符串，如果不提供则使用默认公钥

    Returns:
        str: 解密后的明文密码

    Raises:
        Exception: 解密失败时抛出异常
    """
    try:
        # 使用提供的公钥或默认公钥
        if public_key_string is None:
            public_key_string = DEFAULT_PUBLIC_KEY_STRING

        # Base64解码公钥
        public_key_bytes = base64.b64decode(public_key_string)

        # 加载公钥
        public_key = serialization.load_der_public_key(
            public_key_bytes,
            backend=default_backend()
        )

        # Base64解码加密的密码
        encrypted_bytes = base64.b64decode(encrypted_password)

        # 使用RSA公钥解密（这是Druid的特殊实现方式）
        # 注意：这里我们需要使用低级别的RSA操作
        from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicKey
        from cryptography.hazmat.primitives.asymmetric.utils import decode_dss_signature

        # 获取公钥的数字参数
        public_numbers = public_key.public_numbers()
        n = public_numbers.n
        e = public_numbers.e

        # 将加密数据转换为整数
        encrypted_int = int.from_bytes(encrypted_bytes, byteorder='big')

        # 使用公钥进行"解密"（实际上是RSA的验证操作）
        decrypted_int = pow(encrypted_int, e, n)

        # 转换回字节
        # 确保使用完整的密钥长度
        key_size = (n.bit_length() + 7) // 8
        decrypted_bytes = decrypted_int.to_bytes(key_size, byteorder='big')

        # 移除PKCS1填充
        # PKCS1填充格式: 0x00 0x01 [0xFF...] 0x00 [data]
        if len(decrypted_bytes) < 2 or decrypted_bytes[0] != 0x00 or decrypted_bytes[1] != 0x01:
            raise Exception("无效的PKCS1填充")

        # 找到填充结束位置（0x00字节）
        separator_index = -1
        for i in range(2, len(decrypted_bytes)):
            if decrypted_bytes[i] == 0x00:
                separator_index = i
                break

        if separator_index == -1:
            raise Exception("找不到PKCS1填充分隔符")

        # 提取实际数据
        actual_data = decrypted_bytes[separator_index + 1:]

        # 返回解密后的字符串
        return actual_data.decode('utf-8')

    except Exception as e:
        raise Exception(f"解密Druid密码失败: {str(e)}")


def encrypt_druid_password(plain_password, private_key_string=None):
    """
    使用Druid私钥加密密码（用于测试）
    注意：Druid使用私钥加密，公钥解密的方式

    Args:
        plain_password (str): 明文密码
        private_key_string (str, optional): 私钥字符串，如果不提供则使用默认私钥

    Returns:
        str: 加密后的密码（Base64编码）

    Raises:
        Exception: 加密失败时抛出异常
    """
    try:
        # 使用提供的私钥或默认私钥
        if private_key_string is None:
            private_key_string = DEFAULT_PRIVATE_KEY_STRING

        # Base64解码私钥
        private_key_bytes = base64.b64decode(private_key_string)

        # 加载私钥
        private_key = serialization.load_der_private_key(
            private_key_bytes,
            password=None,
            backend=default_backend()
        )

        # 准备要加密的数据
        data_bytes = plain_password.encode('utf-8')

        # 获取私钥的数字参数
        private_numbers = private_key.private_numbers()
        public_numbers = private_numbers.public_numbers
        n = public_numbers.n
        d = private_numbers.d

        # 添加PKCS1填充
        # PKCS1填充格式: 0x00 0x01 [0xFF...] 0x00 [data]
        key_size = (n.bit_length() + 7) // 8  # 密钥长度（字节）
        max_data_length = key_size - 11  # PKCS1填充需要至少11字节

        if len(data_bytes) > max_data_length:
            raise Exception(f"数据太长，最大支持 {max_data_length} 字节")

        # 构造填充
        padding_length = key_size - len(data_bytes) - 3
        padded_data = bytes([0x00, 0x01]) + bytes([0xFF] * padding_length) + bytes([0x00]) + data_bytes

        # 转换为整数
        padded_int = int.from_bytes(padded_data, byteorder='big')

        # 使用私钥进行"加密"（实际上是RSA的签名操作）
        encrypted_int = pow(padded_int, d, n)

        # 转换回字节
        encrypted_bytes = encrypted_int.to_bytes(key_size, byteorder='big')

        # 返回Base64编码的加密字符串
        return base64.b64encode(encrypted_bytes).decode('utf-8')

    except Exception as e:
        raise Exception(f"加密Druid密码失败: {str(e)}")


def main():
    """命令行工具主函数"""
    parser = argparse.ArgumentParser(description='Druid密码加密/解密工具')
    parser.add_argument('action', choices=['encrypt', 'decrypt'], help='操作类型：encrypt（加密）或 decrypt（解密）')
    parser.add_argument('password', help='要处理的密码')
    parser.add_argument('--private-key', help='自定义私钥（用于加密）')
    parser.add_argument('--public-key', help='自定义公钥（用于解密）')

    args = parser.parse_args()

    try:
        if args.action == 'decrypt':
            result = decrypt_druid_password(args.password, args.public_key)
            print(f"解密结果: {result}")
        elif args.action == 'encrypt':
            result = encrypt_druid_password(args.password, args.private_key)
            print(f"加密结果: {result}")
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
