2025-08-25 11:15:12,653 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:15:12,655 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:15:12,656 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:15:12,657 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:15:12,686 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:15:12,687 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:15:18,082 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:15:18,083 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:15:18,083 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:15:18,084 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:15:18,134 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:15:18,134 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:33:41,937 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:33:41,939 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:33:41,940 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:33:41,941 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:33:42,002 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:33:42,003 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:46:51,923 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:46:51,924 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:46:51,925 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:46:51,926 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:46:51,995 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:46:51,996 - api.app - INFO - 已订阅停止事件频道
2025-08-25 11:49:35,866 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 11:49:35,867 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 11:49:35,868 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 11:49:35,869 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 11:49:35,892 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 11:49:35,893 - api.app - INFO - 已订阅停止事件频道
2025-08-25 14:34:51,459 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 14:34:51,460 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 14:34:51,460 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 14:34:51,462 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 14:34:51,498 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 14:34:51,499 - api.app - INFO - 已订阅停止事件频道
2025-08-25 14:39:39,585 - core.db.redis_service - INFO - 使用Sentinel模式连接Redis，master: sentinel-192.168.33.222-6388, nodes: ['192.168.33.223:6390', '192.168.33.221:6389', '192.168.33.222:6389']
2025-08-25 14:39:39,586 - core.db.redis_service - INFO - Redis Sentinel连接初始化成功，master: sentinel-192.168.33.222-6388
2025-08-25 14:39:39,587 - core.db.redis_service - INFO - Redis订阅任务已启动
2025-08-25 14:39:39,588 - core.db.mysql_pool - INFO - 启动数据库连接池保活任务，间隔 60 秒
2025-08-25 14:39:39,639 - core.db.redis_service - INFO - 已订阅频道: stop_generation
2025-08-25 14:39:39,639 - api.app - INFO - 已订阅停止事件频道
