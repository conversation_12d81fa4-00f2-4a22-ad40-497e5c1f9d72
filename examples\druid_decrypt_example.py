#!/usr/bin/env python3
"""
Druid密码解密使用示例
演示如何在实际项目中使用Druid密码解密功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.druid_decrypt import encrypt_druid_password, decrypt_druid_password
from config.app_config import get_mysql_config


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 原始密码
    original_password = "my_secure_password_123"
    print(f"原始密码: {original_password}")
    
    # 加密密码
    encrypted_password = encrypt_druid_password(original_password)
    print(f"加密后的密码: {encrypted_password}")
    
    # 解密密码
    decrypted_password = decrypt_druid_password(encrypted_password)
    print(f"解密后的密码: {decrypted_password}")
    
    # 验证
    if original_password == decrypted_password:
        print("✅ 加密解密成功！")
    else:
        print("❌ 加密解密失败！")


def example_mysql_config():
    """MySQL配置使用示例"""
    print("\n=== MySQL配置使用示例 ===")
    
    # 模拟一个真实的MySQL密码
    real_password = "MyRealMySQLPassword@2024"
    
    # 使用Druid加密这个密码
    encrypted_password = encrypt_druid_password(real_password)
    print(f"加密的MySQL密码: {encrypted_password}")
    
    # 设置环境变量（模拟生产环境配置）
    os.environ['MYSQL_HOST'] = 'localhost'
    os.environ['MYSQL_PORT'] = '3306'
    os.environ['MYSQL_USER'] = 'myapp'
    os.environ['MYSQL_DB'] = 'myapp_db'
    os.environ['MYSQL_PASSWORD'] = encrypted_password
    os.environ['MYSQL_PASSWORD_ENCRYPTED'] = 'true'
    
    print("\n环境变量设置:")
    print(f"MYSQL_HOST: {os.environ['MYSQL_HOST']}")
    print(f"MYSQL_USER: {os.environ['MYSQL_USER']}")
    print(f"MYSQL_DB: {os.environ['MYSQL_DB']}")
    print(f"MYSQL_PASSWORD: {os.environ['MYSQL_PASSWORD']}")
    print(f"MYSQL_PASSWORD_ENCRYPTED: {os.environ['MYSQL_PASSWORD_ENCRYPTED']}")
    
    # 获取MySQL配置（密码会自动解密）
    config = get_mysql_config()
    
    print(f"\n获取到的MySQL配置:")
    print(f"Host: {config['host']}")
    print(f"Port: {config['port']}")
    print(f"User: {config['user']}")
    print(f"Database: {config['db']}")
    print(f"解密后的密码: {config['password']}")
    
    # 验证密码解密是否正确
    if config['password'] == real_password:
        print("✅ MySQL配置密码解密成功！")
    else:
        print("❌ MySQL配置密码解密失败！")
    
    # 清理环境变量
    for key in ['MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_DB', 'MYSQL_PASSWORD', 'MYSQL_PASSWORD_ENCRYPTED']:
        if key in os.environ:
            del os.environ[key]


def example_production_scenario():
    """生产环境使用场景示例"""
    print("\n=== 生产环境使用场景示例 ===")
    
    # 假设这是从配置文件或环境变量中读取的加密密码
    production_encrypted_passwords = [
        "Kpq/Q6n6fUNX4mLo8/a49RQFSfyRIeYOXSJ9WM8dP08rglmvj/+CBhGo8DxTpuBpsUw+zqG70ymiF9whhFkozQ==",  # mypassword123
        "Xag0apYKgscT9HiHZs106QZ+WqTDgSL+YoIwgpTHLOkxye+7npUokyBK/2mKw64vHXZJmivVjcNUFuqXPMsMIw==",  # 123456
        "dcpLpP4JOgPQFTIFsc15okUTRltMn5x5AVaD+ZGr9Q7sw4tq4Vtmceqg1A2Kfxe4vdnMWzGylQtVAuBPw95dgw=="   # password
    ]
    
    print("解密生产环境中的加密密码:")
    
    for i, encrypted_pwd in enumerate(production_encrypted_passwords, 1):
        try:
            decrypted_pwd = decrypt_druid_password(encrypted_pwd)
            print(f"密码 {i}: {decrypted_pwd}")
        except Exception as e:
            print(f"密码 {i} 解密失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 测试无效的加密密码
    invalid_passwords = [
        "invalid_base64_string",
        "VGhpcyBpcyBub3QgYSB2YWxpZCBlbmNyeXB0ZWQgcGFzc3dvcmQ=",  # 有效的base64但不是正确的加密数据
        "",  # 空字符串
        "not_base64_at_all!"
    ]
    
    for invalid_pwd in invalid_passwords:
        try:
            result = decrypt_druid_password(invalid_pwd)
            print(f"意外成功解密: {invalid_pwd} -> {result}")
        except Exception as e:
            print(f"✅ 正确捕获错误 '{invalid_pwd}': {e}")


if __name__ == '__main__':
    print("Druid密码解密功能使用示例")
    print("=" * 50)
    
    example_basic_usage()
    example_mysql_config()
    example_production_scenario()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")
    
    print("\n使用提示:")
    print("1. 在生产环境中，将加密的密码存储在环境变量中")
    print("2. 设置 MYSQL_PASSWORD_ENCRYPTED=true 启用自动解密")
    print("3. 应用会自动解密密码并建立数据库连接")
    print("4. 建议在生产环境中使用自定义的RSA密钥对以提高安全性")
