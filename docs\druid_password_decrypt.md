# Druid密码解密功能

本项目集成了Druid密码解密功能，支持自动解密使用Druid默认RSA密钥加密的MySQL密码。

## 重要说明

**Druid使用特殊的RSA实现方式：私钥加密，公钥解密**

这与标准的RSA加密（公钥加密，私钥解密）相反。Druid这样做是为了实现一种"签名"机制，确保密码是由拥有私钥的一方加密的。

## 功能特性

- 支持Druid特殊的RSA实现（私钥加密，公钥解密）
- 使用Druid默认RSA密钥对解密密码
- 集成到应用配置模块，自动处理加密密码
- 提供独立的命令行工具进行加密/解密操作
- 包含完整的测试用例和验证

## 使用方法

### 1. 在应用配置中使用

在环境变量中设置以下参数：

```bash
# MySQL连接配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_encrypted_password_here  # Druid加密后的密码
MYSQL_DB=your_database
MYSQL_PASSWORD_ENCRYPTED=true  # 启用密码解密
```

应用会自动检测 `MYSQL_PASSWORD_ENCRYPTED=true` 并解密 `MYSQL_PASSWORD` 中的加密密码。

### 2. 使用命令行工具

#### 解密密码

```bash
python utils/druid_decrypt.py decrypt "your_encrypted_password_here"
```

#### 加密密码（用于测试）

```bash
python utils/druid_decrypt.py encrypt "your_plain_password"
```

#### 使用自定义密钥

```bash
# 使用自定义公钥解密（注意：Druid使用公钥解密）
python utils/druid_decrypt.py decrypt "encrypted_password" --public-key "your_public_key"

# 使用自定义私钥加密（注意：Druid使用私钥加密）
python utils/druid_decrypt.py encrypt "plain_password" --private-key "your_private_key"
```

### 3. 在代码中直接使用

```python
from config.app_config import decrypt_druid_password
from utils.druid_decrypt import encrypt_druid_password

# 解密密码（使用公钥解密）
decrypted = decrypt_druid_password("encrypted_password_here")

# 加密密码（使用私钥加密，用于测试）
encrypted = encrypt_druid_password("plain_password")
```

## RSA密钥信息

本实现使用Druid默认的RSA密钥对：

### 私钥（用于解密）
```
MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAocbCrurZGbC5GArEHKlAfDSZi7gFBnd4yxOt0rwTqKBFzGyhtQLu5PRKjEiOXVa95aeIIBJ6OhC2f8FjqFUpawIDAQABAkAPejKaBYHrwUqUEEOe8lpnB6lBAsQIUFnQI/vXU4MV+MhIzW0BLVZCiarIQqUXeOhThVWXKFt8GxCykrrUsQ6BAiEA4vMVxEHBovz1di3aozzFvSMdsjTcYRRo82hS5Ru2/OECIQC2fAPoXixVTVY7bNMeuxCP4954ZkXp7fEPDINCjcQDywIgcc8XLkkPcs3Jxk7uYofaXaPbg39wuJpEmzPIxi3k0OECIGubmdpOnin3HuCP/bbjbJLNNoUdGiEmFL5hDI4UdwAdAiEAtcAwbm08bKN7pwwvyqaCBC//VnEWaq39DCzxr+Z2EIk=
```

### 公钥（用于加密）
```
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ==
```

## 测试

运行测试用例验证功能：

```bash
python tests/test_druid_decrypt.py
```

测试包括：
- 基本的加密/解密功能测试
- 配置模块集成测试
- MySQL配置自动解密测试

## 依赖

需要安装以下Python包：

```bash
pip install cryptography
```

## 安全注意事项

1. **密钥安全**: 虽然这里使用的是Druid的默认密钥，但在生产环境中建议使用自定义的RSA密钥对。

2. **环境变量**: 确保包含敏感信息的环境变量在生产环境中得到适当保护。

3. **传输安全**: 加密密码在传输和存储时仍需要额外的安全措施。

## 故障排除

### 常见错误

1. **解密失败**: 检查加密密码是否正确，是否使用了正确的密钥对。

2. **导入错误**: 确保已安装 `cryptography` 包。

3. **编码问题**: 确保密码字符串使用正确的编码（UTF-8）。

### 调试

如果解密失败，可以：

1. 使用命令行工具单独测试解密功能
2. 检查环境变量设置是否正确
3. 查看应用日志中的错误信息

## 示例

### 完整的使用示例

```python
import os
from config.app_config import get_mysql_config

# 设置环境变量
os.environ['MYSQL_HOST'] = 'localhost'
os.environ['MYSQL_USER'] = 'root'
os.environ['MYSQL_DB'] = 'test_db'
os.environ['MYSQL_PASSWORD'] = 'your_druid_encrypted_password'
os.environ['MYSQL_PASSWORD_ENCRYPTED'] = 'true'

# 获取配置，密码会自动解密
config = get_mysql_config()
print(f"解密后的密码: {config['password']}")
```

### 运行示例和测试

```bash
# 运行完整的功能示例
python examples/druid_decrypt_example.py

# 运行测试用例
python tests/test_druid_decrypt.py

# 使用命令行工具
python utils/druid_decrypt.py encrypt "your_password"
python utils/druid_decrypt.py decrypt "encrypted_password_string"
```

## 实现状态

✅ **已完成的功能:**
- Druid密码解密核心功能
- 集成到应用配置模块
- 命令行工具支持
- 完整的测试用例
- 详细的文档和示例
- 错误处理和安全检查

✅ **测试验证:**
- 基本加密/解密功能测试通过
- 配置模块集成测试通过
- MySQL配置自动解密测试通过
- 错误处理测试通过
- 命令行工具测试通过
